# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Aether AA (Aether Adaptive Autosar) is an open-source C++17 implementation of Adaptive AUTOSAR. The project follows a modular architecture with three main components:

- **Core**: Header-only library providing fundamental types like `Result`, error handling, and instance management
- **Com**: Communication module implementing DDS-based event handling and pub/sub patterns using Fast-DDS
- **Utils**: Utility library providing logging with spdlog and memory management with jemalloc

## Build System

The project uses CMake 3.28+ with C++17 standard. Build the project with:

```bash
# Configure build (from project root)
cmake -B build -G Ninja

# Build all targets
cmake --build build

# Or use ninja directly
cd build && ninja
```

### Key Build Targets

- `aether_core` - Header-only interface library (Core module)
- `aether_com` - Shared library for communication (Com module)  
- `aether_utils` - Shared library for utilities (Utils module)
- `result_benchmark` - Performance benchmarks for Result type
- `result_production_benchmark` - Production-quality Result benchmarks
- `comtest` - Communication module test executable

## Dependencies

- **Fast-DDS 3.x**: DDS implementation for communication
- **fastcdr 2.x**: CDR serialization library
- **spdlog**: Logging framework
- **jemalloc**: Memory allocator
- **Threads**: Standard threading support

Fast-DDS is expected to be installed at `~/Fast-DDS/install` (configured in Com/CMakeLists.txt).

## Architecture Patterns

### Result Type Pattern
The project implements a Rust-like `Result<T, E>` type in Core for error handling. Prefer using `Result` over exceptions for all public APIs.

### Static Polymorphism
Communication events use CRTP (Curiously Recurring Template Pattern) for static polymorphism between different protocol implementations (DDS, SOME/IP).

### Header-Only Design
Core module is entirely header-only for optimal performance and template usage. All implementations are in headers with INTERFACE library linkage.

## Code Organization

```
Core/           - Header-only fundamental types and error handling
  instance_Specifier.hpp
  result.hpp
  error_*.hpp
  
Com/            - Communication layer with DDS backend
  inc/event/    - Event system (static polymorphic design)
  inc/          - Publisher/Subscriber interfaces
  
Utils/          - Utility libraries
  log/          - Logging infrastructure
```

## Development Notes

- All new code must follow C++17 standards with RAII patterns
- Use `ara::core::Result<T>` for error handling instead of exceptions
- Event handling follows AUTOSAR specifications with async patterns
- Communication layer is protocol-agnostic but currently implements DDS binding
- Template metaprogramming is used extensively for compile-time optimizations
- 写代码之前要用context7