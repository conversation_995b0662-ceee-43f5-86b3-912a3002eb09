//
// Created by 李浩楠 on 25-6-28.
//

#ifndef DDS_EVENT_HPP
#define DDS_EVENT_HPP

#include "com_error_domain.hpp"
#include "event.hpp"
#include "result.hpp"
#include "dds_qos_wrapper.hpp"

// FastDDS 头文件
#include <cstddef>
#include <cstdint>
#include <fastdds/dds/core/policy/QosPolicies.hpp>
#include <fastdds/dds/core/status/StatusMask.hpp>
#include <fastdds/dds/core/status/SubscriptionMatchedStatus.hpp>
#include <fastdds/dds/domain/DomainParticipant.hpp>
#include <fastdds/dds/domain/DomainParticipantFactory.hpp>
#include <fastdds/dds/domain/qos/DomainParticipantQos.hpp>
#include <fastdds/dds/subscriber/DataReader.hpp>
#include <fastdds/dds/subscriber/DataReaderListener.hpp>
#include <fastdds/dds/subscriber/Subscriber.hpp>
#include <fastdds/dds/subscriber/qos/DataReaderQos.hpp>
#include <fastdds/dds/subscriber/qos/SubscriberQos.hpp>
#include <fastdds/dds/topic/Topic.hpp>
#include <fastdds/dds/topic/qos/TopicQos.hpp>
#include <fastdds/dds/core/detail/DDSReturnCode.hpp>
#include <memory>
#include <string>
#include <vector>

namespace ara::com::network_binding::proxy::dds
{
    // 使用FastDDS命名空间
    using namespace eprosima::fastdds::dds;

    // FastDDS类型别名
    using DDSDataReader = DataReader;
    using DDSSubscriber = Subscriber;
    using DDSTopic = Topic;
    using DDSDomainParticipant = DomainParticipant;
    using DDSDataReaderListener = DataReaderListener;


    /**
     * @brief DDS协议特定的事件实现
     * @tparam SampleType 事件数据类型
     */
    template <typename SampleType>
    class DDSEventImpl
    {
        using Base = Event<DDSEventImpl<SampleType>, SampleType>;
        friend Base;  // 允许基类访问私有成员

    public:
        /**
         * @brief 构造函数
         * @param topic_name 事件对应的Topic名称
         * @param qos_config DDS QoS配置
         */
        explicit DDSEventImpl(const std::string& topic_name,
                   const ProxyQosSettings& qos_config = ProxyQosSettings())
            : Base(),  // 调用基类默认构造函数
              topic_name_(topic_name),
              qos_config_(qos_config),
              dds_domain_participant_(nullptr),
              dds_subscriber_(nullptr),
              dds_topic_(nullptr),
              dds_data_reader_(nullptr),
              dds_listener_(nullptr) {}

        /**
         * @brief 析构函数 - 确保DDS资源正确清理
         */
        ~DDSEventImpl()
        {
            
        }

        Result<void> Test()
        {

        }
        

        /**
         * @brief 设置DDS DataReaderListener
         * @return 创建结果
         */
        template <typename ListenerType>
        core::Result<void> SetDDSDataReaderListener(std::shared_ptr<ListenerType> listener)
        {
            // 编译期保证ListenerType必须是DDSDataReaderListener的子类
            static_assert(std::is_base_of_v<DDSDataReaderListener, ListenerType>,
                          "ListenerType must be a subclass of DDSDataReaderListener");

            if (listener)
            {
                dds_listener_ = listener;
            }
            return {};
        }

    private:
        // DDS特定配置
        std::string topic_name_;
        ProxyQosSettings qos_config_;

        // DDS实体管理（按照销毁的逆序声明）
        std::shared_ptr<DDSDomainParticipant> dds_domain_participant_;
        std::shared_ptr<DDSSubscriber> dds_subscriber_;
        std::shared_ptr<DDSTopic> dds_topic_;
        std::shared_ptr<DDSDataReader> dds_data_reader_;
        std::shared_ptr<DDSDataReaderListener> dds_listener_;

        /**
         * @brief 执行DDS特定的订阅操作（由基类调用）
         * @param maxSampleCount 最大样本数量
         * @return 订阅结果
         */
        Result<void> Subscribe(size_t maxSampleCount)
        {
            // 1. 创建DDS DomainParticipant（如果不存在）
            if (!dds_domain_participant_)
            {
                auto result = CreateDDSDomainParticipant();
                if (result.is_err())
                {
                    return result;
                }
            }

            // 2. 创建DDS Subscriber（如果不存在）
            if (!dds_subscriber_)
            {
                auto result = CreateDDSSubscriber();
                if (result.is_err())
                {
                    return result;
                }
            }

            // 3. 创建DDS Topic（如果不存在）
            if (!dds_topic_)
            {
                auto result = CreateDDSTopic();
                if (result.is_err())
                {
                    return result;
                }
            }

            // 4. 创建并配置DDS DataReader
            auto result = CreateDDSDataReader(maxSampleCount);
            if (result.is_err())
            {
                return result;
            }

            // 5. 设置DataReaderListener
            result = ConfigureDDSDataReaderListener();
            if (result.is_err())
            {
                return result;
            }

            return {};  // 成功
        }

        /**
         * @brief 执行DDS特定的取消订阅操作（由基类调用）
         */
        void Unsubscribe()
        {
            DestroyDDSDataReader();
        }

        /**
         * @brief 获取DDS特定的订阅状态（由基类调用）
         * @return 当前订阅状态
         */
        SubscriptionState GetSubscriptionState() const
        {
            if (!dds_data_reader_)
            {
                return SubscriptionState::kNotSubscribed;
            }

            // 使用FastDDS API检查订阅匹配状态
            SubscriptionMatchedStatus status;
            ReturnCode_t ret = dds_data_reader_->get_subscription_matched_status(status);

            if (ret != RETCODE_OK)
            {
                return Base::is_subscribed_ ? SubscriptionState::kSubscriptionPending : SubscriptionState::kNotSubscribed;
            }

            // 检查是否有匹配的Publisher
            if (status.current_count > 0)
            {
                return SubscriptionState::kSubscribed;
            }
            else
            {
                return Base::is_subscribed_ ? SubscriptionState::kSubscriptionPending : SubscriptionState::kNotSubscribed;
            }
        }

        /**
         * @brief 获取DDS特定的新样本（由基类调用）
         * @param maxSamples 最大样本数量
         * @return 获取的样本
         */
        std::vector<std::shared_ptr<SampleType>> GetNewSamples(size_t maxSamples)
        {
            if (!dds_data_reader_)
            {
                return {};
            }

            // 实际的DDS take操作
            return PerformDDSTake(maxSamples);
        }

        /**
         * @brief 创建DDS DomainParticipant
         * @return 创建结果
         */
        ara::core::Result<void> CreateDDSDomainParticipant()
        {
            auto factory = DomainParticipantFactory::get_instance();
            if (!factory)
            {
                core::ErrorCode error(ComErrorCode::kDDSParticipantCreationFailure);
                error.setErrorMessage("Failed to get DomainParticipantFactory instance");
                return core::Err(error);
            }

            auto participant = factory->create_participant(0, qos_config_.participant_qos);
            if (participant)
            {
                // 正确使用shared_ptr构造函数和自定义删除器
                dds_domain_participant_ = std::shared_ptr<DDSDomainParticipant>(
                    participant,
                    [](DDSDomainParticipant* p) {
                        if (p) {
                            DomainParticipantFactory::get_instance()->delete_participant(p);
                        }
                    });
            }

            if (!dds_domain_participant_)
            {
                core::ErrorCode error(ComErrorCode::kDDSParticipantCreationFailure);
                error.setErrorMessage("Failed to create DDS DomainParticipant");
                return core::Err(error);
            }

            return core::Ok();
        }

        /**
         * @brief 创建DDS Subscriber
         * @return 创建结果
         */
        core::Result<void> CreateDDSSubscriber()
        {
            if (!dds_domain_participant_)
            {
                return Err(ComErrorCode::kDDSParticipantUnavailable);
            }

            if (auto subscriber = dds_domain_participant_->create_subscriber(qos_config_.subscriber_qos))
            {
                // 正确使用shared_ptr构造函数
                dds_subscriber_ = std::shared_ptr<DDSSubscriber>(
                    subscriber,
                    [parent = dds_domain_participant_](DDSSubscriber* p) {
                        if (p && parent) {
                            parent->delete_subscriber(p);
                        }
                    });
            }

            if (!dds_subscriber_)
            {
                core::ErrorCode error(ComErrorCode::kDDSSubscriberCreationFailure);
                error.setErrorMessage("Failed to create DDS Subscriber");
                return core::Err(error);
            }

            return core::Ok();
        }

        /**
         * @brief 创建DDS Topic
         * @return 创建结果
         */
        core::Result<void> CreateDDSTopic()
        {
            if (!dds_domain_participant_)
            {
                core::ErrorCode error(ComErrorCode::kDDSTopicCreationFailure);
                error.setErrorMessage("DomainParticipant not available for Topic creation");
                return core::Err(error);

            }

            std::string type_name = typeid(SampleType).name();
            auto topic = dds_domain_participant_->create_topic(topic_name_, type_name, qos_config_.topic_qos);
            if (topic)
            {
                dds_topic_ = std::make_shared<DDSTopic>(
                    topic, [parent = dds_domain_participant_](DDSTopic* p) { parent->delete_topic(p); });
            }

            if (!dds_topic_)
            {
                core::ErrorCode error(ComErrorCode::kDDSTopicCreationFailure);
                error.setErrorMessage("Failed to create DDS Topic: " + topic_name_);
                return core::Err(error);
            }

            return {};
        }

        /**
         * @brief 创建DDS DataReader
         * @param maxSampleCount 最大样本数量
         * @return 创建结果
         */
        core::Result<void> CreateDDSDataReader(size_t maxSampleCount)
        {
            if (!dds_subscriber_ || !dds_topic_)
            {
                core::ErrorCode error(ComErrorCode::kDDSDataReaderCreationFailure);
                error.setErrorMessage("Subscriber or Topic not available for DataReader creation");
                return core::Err(error);

            }

            // 配置DataReader QoS
            auto qos_result = ConfigureDDSDataReaderQoS(maxSampleCount);
            if (qos_result.is_err())
            {
                return qos_result;
            }

            // 使用FastDDS API创建DataReader
            if (auto data_reader = dds_subscriber_->create_datareader(dds_topic_.get(), qos_config_.reader_qos))
            {
                dds_data_reader_ = std::make_shared<DDSDataReader>(
                    data_reader, [parent = dds_subscriber_](DDSDataReader* p) { parent->delete_datareader(p); });
            }

            if (!dds_data_reader_)
            {
                core::ErrorCode error(ComErrorCode::kDDSDataReaderCreationFailure);
                error.setErrorMessage("Failed to create DDS DataReader");
                return core::Err(error);
            }

            return {};
        }

        /**
         * @brief 配置DDS DataReader的QoS
         * @param maxSampleCount 最大样本数量
         * @return 配置结果
         */
        core::Result<void> ConfigureDDSDataReaderQoS(size_t maxSampleCount)
        {
            auto& data_reader_qos_ = qos_config_.reader_qos;

            // 配置History QoS - 按照AUTOSAR AP规范
            data_reader_qos_.history().kind = KEEP_LAST_HISTORY_QOS;
            data_reader_qos_.history().depth = static_cast<int32_t>(maxSampleCount);

            // 配置Resource Limits
            data_reader_qos_.resource_limits().max_samples = static_cast<int32_t>(maxSampleCount);
            data_reader_qos_.resource_limits().max_instances = 1;
            data_reader_qos_.resource_limits().max_samples_per_instance = static_cast<int32_t>(maxSampleCount);

            // 配置Reliability QoS
            data_reader_qos_.reliability().kind = RELIABLE_RELIABILITY_QOS;

            // 配置Durability QoS
            data_reader_qos_.durability().kind = TRANSIENT_LOCAL_DURABILITY_QOS;

            return {};
        }

        /**
         * @brief 配置DDS DataReaderListener
         * @return 配置结果
         */
        core::Result<void> ConfigureDDSDataReaderListener()
        {
            if (!dds_data_reader_)
            {
                core::ErrorCode error(ComErrorCode::kDDSDataReaderUnavailable);
                error.setErrorMessage("DataReader not available for Listener creation");
                return core::Err(error);
            }

            // 设置Listener到DataReader
            if (dds_listener_)
            {
                auto ret = dds_data_reader_->set_listener(dds_listener_.get(), StatusMask::data_available());
                if (ret != RETCODE_OK)
                {
                    core::ErrorCode error(ComErrorCode::kDDSDataReaderListenerCreationFailure);
                    error.setErrorMessage("Failed to set DataReaderListener");
                    return core::Err(error);
                }
            }

            return {};
        }

        /**
         * @brief 销毁DDS DataReader
         */
        void DestroyDDSDataReader()
        {
            if (dds_data_reader_ && dds_subscriber_)
            {
                dds_subscriber_->delete_datareader(dds_data_reader_.get());
                dds_data_reader_.reset();
            }
        }

        /**
         * @brief 执行DDS DataReader::take()操作
         * @param maxSamples 最大样本数量
         * @return 获取的样本
         */
        std::vector<std::shared_ptr<SampleType>> PerformDDSTake(size_t maxSamples)
        {
            std::vector<std::shared_ptr<SampleType>> samples;

            if (!dds_data_reader_)
            {
                return samples;
            }

            // 这里需要根据实际的SampleType进行类型特化
            // 暂时返回空样本，实际实现需要使用FastDDS的take操作
            // TODO: 实现类型特化的take操作

            return samples;
        }
    };

}  // namespace aether::com::network_binding::dds::proxy

#endif  // DDS_EVENT_HPP
