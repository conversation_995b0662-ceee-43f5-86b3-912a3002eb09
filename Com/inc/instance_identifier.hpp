
#pragma once
#include <string_view>
#include <utility>
#include "result.hpp"
#include "com_error_domain.hpp"
#include <string>
namespace ara::com{
using StringView = std::string_view;

// Implementaion - [SWS_CM_00302]: ara::com::InstanceIdentifier
class InstanceIdentifier final
{
    
public:
    // SWS_CM_00302: 命名构造函数 Create()
        /**
         * @brief 从序列化格式字符串创建InstanceIdentifier实例
         * 该函数接收一个序列化格式的字符串，并尝试将其转换为InstanceIdentifier对象。
         * 函数会验证输入字符串的格式是否符合规范要求。
         * 
         * @param serializedFormat Vendor Specific
         * @return ara::core::Result<InstanceIdentifier> 
         *         成功时返回包含InstanceIdentifier对象的Result，
         *         失败时返回包含ComErrorCode::kInvalidInstanceIdentifierString错误码的Result
         */
        static ara::core::Result<InstanceIdentifier> Create(StringView serializedFormat) noexcept
        {
            // 这是一个简化的示例验证逻辑。实际实现中会有更复杂的格式检查。
            // 假设这里我们检查字符串是否为空，或者是否包含不允许的字符。
            //TODO: <Low> 后续增加复杂验证逻辑。
            if (serializedFormat.empty() || serializedFormat.find_first_of("~!@#$%^&*()_+=") != StringView::npos)
            {
                // 如果字符串表示格式损坏或不符合提供商规范，必须返回错误码 ComErrc::kInvalidInstanceIdentifierString
                // return ara::core::Result<InstanceIdentifier>(Make(ComErrc::kInvalidInstanceIdentifierString));
                return core::Err(ComErrorCode::kInvalidInstanceIdentifierString);
            }
            // return ara::core::Result<InstanceIdentifier>(InstanceIdentifier(serializedFormat));
            return core::Ok(InstanceIdentifier(serializedFormat));
        }

    // SWS_CM_00302: 显式类构造函数
    // 如果字符串格式损坏或不符合提供商规范，必须抛出 ComException 异常
    explicit InstanceIdentifier(StringView serializedFormat)
        : id_string_(serializedFormat)
    {
        // TODO: <Low> 同样是简化的验证逻辑
        if (serializedFormat.empty() || serializedFormat.find_first_of("~!@#$%^&*()_+=") != StringView::npos)
        {
            //TODO: <High> ComExecption 
            // throw ComException(Make(ComErrc::kInvalidInstanceIdentifierString));
        }
    }

    // SWS_CM_00302: 拷贝构造函数 (EqualityComparable, LessThanComparable, CopyAssignable 要求的一部分)
    InstanceIdentifier(const InstanceIdentifier& other) = default;

    // SWS_CM_00302: 移动构造函数
    InstanceIdentifier(InstanceIdentifier&& other) noexcept  = default;

    // SWS_CM_00302: 拷贝赋值操作符 (CopyAssignable)
    InstanceIdentifier& operator=(const InstanceIdentifier& other) = default;

    // SWS_CM_00302: 移动赋值操作符
    InstanceIdentifier& operator=(InstanceIdentifier&& other) noexcept = default;

    // SWS_CM_00302: 析构函数
    ~InstanceIdentifier() noexcept = default;

    // SWS_CM_00302: ToString() 方法
    // 字符串表示格式由 Communication Management 软件提供商决定，不进行标准化
    StringView ToString() const noexcept { return id_string_; }

    // SWS_CM_00302: operator== (EqualityComparable)
    bool operator==(const InstanceIdentifier& other) const noexcept { return id_string_ == other.id_string_; }

    // SWS_CM_00302: operator< (LessThanComparable)
    bool operator<(const InstanceIdentifier& other) const noexcept { return id_string_ < other.id_string_; }

    // SWS_CORE_08044: operator!= (非成员函数，但为了完整性，类内部实现) [8]
    bool operator!=(const InstanceIdentifier& other) const noexcept { return !(*this == other); }
    // SWS_CORE_08044: operator!= (与 StringView 比较，非成员函数，但为了完整性，类内部实现) [9]
    bool operator!=(StringView other) const noexcept { return id_string_ != other; }


private:
    std::string id_string_; // 内部存储标识符字符串
};

// SWS_CORE_08043: 全局 operator== (StringView on lhs)
inline bool operator==(StringView lhs, const InstanceIdentifier& rhs) noexcept {
    return lhs == rhs.ToString();
}

// SWS_CORE_08044: 全局 operator!= (StringView on lhs)
inline bool operator!=(StringView lhs, const InstanceIdentifier& rhs) noexcept {
    return lhs != rhs.ToString();
}

} // namespace ara::com