cmake_minimum_required(VERSION 3.28)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Performance optimization flags
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG -flto")
endif()

# Create main library as header-only (all implementations are now in headers)
add_library(aether_core INTERFACE)

target_sources(aether_core INTERFACE
    FILE_SET HEADERS
    BASE_DIRS ${CMAKE_CURRENT_SOURCE_DIR}
    FILES
        instance_specifier.hpp
        error_code.hpp
        error_domain.hpp
        error_types.hpp
        result.hpp
        initialization.hpp
        vector.hpp
)

target_include_directories(aether_core INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
    $<INSTALL_INTERFACE:include/ara/core>
)

target_link_libraries(aether_core INTERFACE
    aether_utils
)
        
# Compiler-specific optimizations for interface library
target_compile_options(aether_core INTERFACE
    $<$<CXX_COMPILER_ID:GNU>:-Wall -Wextra -Wpedantic -Wno-unused-parameter>
    $<$<CXX_COMPILER_ID:Clang>:-Wall -Wextra -Wpedantic -Wno-unused-parameter>
    $<$<CXX_COMPILER_ID:MSVC>:/W4>
)

target_compile_features(aether_core INTERFACE cxx_std_17)

# Enable Link Time Optimization for Release builds
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    set_property(TARGET aether_core PROPERTY INTERPROCEDURAL_OPTIMIZATION TRUE)
endif()



# Correct Err usage example
add_executable(correct_err_usage
    example/correct_err_usage.cpp
)

target_link_libraries(correct_err_usage PRIVATE aether_core)

# Test Vector
add_executable(test_vector test/test_vector.cpp)
target_link_libraries(test_vector PRIVATE aether_utils aether_core)

