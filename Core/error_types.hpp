/*
 * Copyright (c) 2024 leehaonan <<EMAIL>>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * @file           : error_types.hpp
 * <AUTHOR> leehaonan
 * @brief          : Common type definitions for error handling
 * @date           : 2024/6/25
 * @version        : R23-11
 */

#ifndef THREADPRACTISEYEEAH_ERROR_TYPES_HPP
#define THREADPRACTISEYEEAH_ERROR_TYPES_HPP

#include <cstdint>

namespace ara::core {

using ErrorDomainIdType = std::uint64_t;
using ErrorCodeType = std::int32_t;
using ErrorSupportDataType = std::int32_t;

}  // namespace ara::core

#endif  // THREADPRACTISEYEEAH_ERROR_TYPES_HPP
