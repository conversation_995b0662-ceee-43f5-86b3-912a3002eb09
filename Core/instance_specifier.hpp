//
// Created by 15531 on 2025/4/21.
//

#ifndef INSTANCE_SPECIFIER_HPP
#define INSTANCE_SPECIFIER_HPP

#include <cctype>
#include <cstddef>
#include <string>
#include <string_view>
#include "core_error_domain.hpp"
#include "result.hpp"
namespace ara::core
{
    // [SWS_CORE_08001]
    class InstanceSpecifier final
    {
        // [SWS_CORE_08021]
        explicit InstanceSpecifier(std::string_view metaModelIdentifier);

        // [SWS_CORE_08022]
        InstanceSpecifier(const InstanceSpecifier &other) = default;

        // [SWS_CORE_08023]
        InstanceSpecifier(InstanceSpecifier &&other) = default;

        // [SWS_CORE_08024]
        InstanceSpecifier &operator=(const InstanceSpecifier &other);

        // [SWS_CORE_08025]
        InstanceSpecifier &operator=(InstanceSpecifier &&other) noexcept;

        // [SWS_CORE_08029]
        ~InstanceSpecifier() noexcept;


        /**
         * @brief [SWS_CORE_08032]-创建InstanceSpecifier实例，不抛出异常
         * @param metaModelIdentifier 元模型标识符
         * @return Result<InstanceSpecifier> 返回创建结果，成功时包含InstanceSpecifier实例，失败时包含错误信息
         * @link [SWS_CORE_10200] [SWS_CORE_10203]
         */
        static Result<InstanceSpecifier> Create(std::string_view metaModelIdentifier) noexcept {
            auto result = validatePath(metaModelIdentifier);

            // 如果验证失败，传播错误
            if (result.is_err()) {
                return Err(result.unwrap_err());
            }

            // 验证成功，创建 InstanceSpecifier 实例
            return Ok(InstanceSpecifier(metaModelIdentifier));
        }

        // [SWS_CORE_08042]
        bool operator==(const InstanceSpecifier &other) const noexcept{
            return (spec_string_ == other.spec_string_);
        }

        // [SWS_CORE_08043]
        bool operator==(std::string_view other) const noexcept{
            return (spec_string_ == other);
        }

        // [SWS_CORE_08044]
        bool operator!=(const InstanceSpecifier &other) const noexcept{
            return (spec_string_ != other.spec_string_);
        }

        // [SWS_CORE_08045]
        bool operator!=(std::string_view other) const noexcept{
            return (spec_string_ != other);
        }

        // [SWS_CORE_08046]
        bool operator<(const InstanceSpecifier &other) const noexcept{
            return (spec_string_ < other.spec_string_);
        }

        // [SWS_CORE_08041]
        std::string_view ToString() const noexcept{
            return spec_string_;
        }


        // follow
        // Executable.shortName/RootSwComponentPrototype.shortName/SwComponentPrototype.shortName/.../PortPrototype.shortName
        static Result<void> validatePath(std::string_view path) noexcept
        {
            if (path.empty()) return Err(CoreErrorCode::kInvalidMetaModelPath);

            if (path.front() == '/' || path.back() == '/') return Err(CoreErrorCode::kInvalidMetaModelPath);
            if (path.find("//") != std::string_view::npos) return Err(CoreErrorCode::kInvalidMetaModelPath);

            //检查每个路径段的shortname格式
            size_t start = 0;
            size_t pos = 0; // 分隔符位置
            while((pos = path.find('/', start)) != std::string_view::npos){
                if (!isValidShortName(path.substr(start, pos - start))) return Err(CoreErrorCode::kInvalidMetaModelShortname);
                start = pos + 1;
            }

            // 检查最后一个路径段
            if(start < path.size()){
                if (!isValidShortName(path.substr(start))) return Err(CoreErrorCode::kInvalidMetaModelShortname);

            }
            return Ok();
        }

        static bool isValidShortName(std::string_view shortName) noexcept
        {
            if (shortName.empty()) return false;

            // 判断第一个字符是否为字母
            if(!std::isalpha(shortName[0])) return false;

            //只有字母、数字、下划线才被允许
            for(auto c : shortName){
                if(!std::isalnum(c) && c != '_') return false;
            }

            return true;

            
        }

    private:
        std::string spec_string_;
    };

    // [SWS_CORE_08081]
    bool operator==(std::string_view lhs, const InstanceSpecifier &rhs) noexcept;

    // [SWS_CORE_08082]
    bool operator!=(std::string_view lhs, const InstanceSpecifier &rhs) noexcept;


}  // namespace ara::core


#endif  // INSTANCE_SPECIFIER_HPP
