#ifndef CORE_ERROR_DOMAIN_H_
#define CORE_ERROR_DOMAIN_H_
#include "error_code.hpp"
#include "error_types.hpp"

namespace ara::core
{
    // [SWS_CORE_05200]
    enum class CoreErrorCode : ErrorCodeType
    {
        kInvalidArgument = 22,            // an invalid argument was passed to a function
        kInvalidMetaModelShortname = 137, // given string is not a valid model element shortname
        kInvalidMetaModelPath = 138       // missing or invalid path to model element
    };

    // [SWS_CORE_05211]
    class CoreErrorDomain final : public ErrorDomain
    {
    public:
        friend constexpr const CoreErrorDomain& GetCoreErrorDomain() noexcept;


        CoreErrorDomain() = delete;

        [[nodiscard]] std::string_view Name() const noexcept override { return "CoreErrorDomain"; }

        [[nodiscard]] std::string_view Message(CodeType errorCode) const noexcept override
        {
            thread_local static std::string cached_message;

            switch (static_cast<CoreErrorCode>(errorCode))
            {
                case CoreErrorCode::kInvalidArgument:
                {
                    cached_message = "Maximum sample count not realizable";
                }
                default:
                {
                    cached_message = "Unknown COM error: " + std::to_string(errorCode);
                }
            }
            return cached_message;
        }

        void ThrowAsException(const core::ErrorCode& errorCode) const override
        {
            std::string error_msg = "CoreErrorDomain exception: ";
            error_msg += Message(errorCode.Value());
            throw std::runtime_error(error_msg);
        }

        // 有参构造函数，使用 CoreErrorDomain 的 ID（仅供内部使用）
        explicit constexpr CoreErrorDomain(const Idtype id) noexcept :
            ErrorDomain(id)
        {
        }
    };


    // C++17 兼容的全局静态实例
    namespace detail
    {
        inline constexpr CoreErrorDomain g_core_error_domain{CoreErrorDomainId};
    }


    constexpr const CoreErrorDomain& GetCoreErrorDomain() noexcept
    {
        return detail::g_core_error_domain;
    }



    constexpr ErrorCode MakeErrorCode(CoreErrorCode code, ErrorDomain::SupportDataType data) noexcept
    {
        return ErrorCode(static_cast<ErrorCodeType>(code), GetCoreErrorDomain(), data);
    }
}


#endif  // CORE_ERROR_DOMAIN_H_
